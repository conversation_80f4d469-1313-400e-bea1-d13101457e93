from typing import Generator, Optional

from loguru import logger

from box_top import BoxTop
from box_top.box_top_detector_protocol import BoxTopDetectorProtocol


class BoxDetector:
    """
    Box detector that uses a BoxTop stream from a box top detector.

    The algorithm is simple:
    - If a BoxTop is detected with center y >= 0, it gets yielded
    - Then it waits for a BoxTop with center y < -reset_offset before yielding the next box
    - This prevents yielding the same box twice and reduces false positives

    Uses dependency injection for the box top detector.
    """

    def __init__(self, box_top_detector: BoxTopDetectorProtocol, reset_offset: float = -0.02):
        """
        Initialize the BoxDetector with a box top detector.

        Args:
            box_top_detector: Box top detector implementation following BoxTopDetectorProtocol
            reset_offset: Offset below 0 that the center y must reach before reset (prevents false positives)
        """
        self.box_top_detector = box_top_detector
        self.reset_offset = reset_offset
        self._waiting_for_reset = True

    def detect_boxes(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box detection and yield BoxTop objects as a stream.
        Only yields boxes when they cross the detection threshold.

        Yields:
            BoxTop objects or None if detection fails
        """
        logger.trace("Starting box detection")
        self._waiting_for_reset = True

        for box_top in self.box_top_detector.detect_box_tops():
            if box_top is None:
                yield None
                continue

            center_y = box_top.center[1]
            logger.trace(f"BoxTop center y: {center_y:.3f}")

            if self._waiting_for_reset:
                # We're waiting for center y < -reset_offset to reset the detection state
                reset_threshold = -self.reset_offset
                if center_y < reset_threshold:
                    logger.trace(
                        f"Reset condition met (center y {center_y:.3f} < {reset_threshold:.3f}), ready for next box"
                    )
                    self._waiting_for_reset = False
                # Don't yield anything while waiting for reset
                yield None
            else:
                # We're ready to detect a new box
                if center_y >= 0:
                    logger.trace(f"Box detected! Center y: {center_y:.3f}")
                    self._waiting_for_reset = True
                    yield box_top
                else:
                    # Box not yet at detection threshold
                    yield None


if __name__ == "__main__":
    # Example usage with mock detector
    from pprint import pprint
    from box_top.box_top_detector_mock import BoxTopDetectorMock

    # Create a mock box top detector
    mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=True)

    # Create the box detector with dependency injection and a reset offset
    # The reset offset prevents false positives by requiring the box to move further away
    box_detector = BoxDetector(mock_detector, reset_offset=0.1)

    # Example 1: Using context manager (recommended)
    print("=== Using context manager ===")
    try:
        for detected_box in box_detector.detect_boxes():
            if detected_box is not None:
                print("Detected box:")
                pprint(detected_box)
    except KeyboardInterrupt:
        logger.info("Stopping box detection...")
