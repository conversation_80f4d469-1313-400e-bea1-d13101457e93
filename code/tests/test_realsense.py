"""Hardware-dependent tests that require actual RealSense camera."""

import time
import pytest
from unittest.mock import patch
from camera.camera_realsense import RealSenseCamera
from box_top.box_top_detector import BoxTopDetector
from box_top.visualizer import NoOpVisualizer


def realsense_available():
    """Check if RealSense hardware is available."""
    try:
        camera = RealSenseCamera()
        with camera:
            camera.get_pointcloud()
        return True
    except RuntimeError as e:
        if e.args[0] == "No device connected":
            return False
        else:
            return True
    except Exception:
        return True


realsense = pytest.mark.skipif(
    not realsense_available(), reason="RealSense hardware not available"
)


@pytest.fixture(autouse=True)
def slow_down_tests():
    yield
    time.sleep(2)  # Opening and closing the camera in rapid succession can cause issues


@pytest.mark.hardware
@realsense
class TestRealSenseCamera:
    """Test cases for RealSenseCamera that require actual hardware."""

    def test_camera_initialization(self):
        """Test RealSense camera initialization."""
        # This test requires actual RealSense hardware
        camera = RealSenseCamera()

        # Test that camera can be created (may fail if no hardware)
        assert camera is not None
        assert hasattr(camera, "pipeline")
        assert hasattr(camera, "config")

    @realsense
    def test_camera_start_capture(self):
        """Test starting camera capture."""
        camera = RealSenseCamera()

        try:
            # This will fail if no hardware is connected
            result = camera._start_capture()
            assert result
            assert camera.get_pointcloud() is not None
        finally:
            camera._stop_capture()

    @realsense
    def test_camera_context_manager(self):
        """Test camera context manager."""
        camera = RealSenseCamera()

        with camera:
            # If hardware is available, should work
            pcd = camera.get_pointcloud()
            assert pcd is not None

    @realsense
    def test_get_device_info(self):
        """Test getting device info."""
        camera = RealSenseCamera()

        with camera:
            info = camera.get_device_info()
            assert type(info) is dict

    @realsense
    def test_get_pointcloud(self):
        """Test getting point cloud."""
        camera = RealSenseCamera()

        with camera:
            pcd = camera.get_pointcloud()
            assert pcd is not None
            assert len(pcd.points) > 0

    @realsense
    def test_set_exposure(self):
        """Test setting exposure."""
        camera = RealSenseCamera()

        with camera:
            assert camera.set_exposure(1000)
            assert camera.set_exposure(800)
            assert camera.enable_auto_exposure()

    @realsense
    def test_get_frames(self):
        """Test getting frames."""
        camera = RealSenseCamera(
            depth_resolution=RealSenseCamera.Resolution.RES_640x360,
            color_resolution=RealSenseCamera.Resolution.RES_640x360,
            frame_rate=RealSenseCamera.FrameRate.FPS_30,
            enable_decimation=False,
        )

        with camera:
            frames = camera.get_frames()
            assert frames is not None
            assert len(frames) == 2
            (color_image, depth_image) = frames
            assert color_image.shape == (360, 640, 3)
            assert depth_image.shape == (360, 640)

    @realsense
    def test_decimation(self):
        """Test getting frames."""
        decimation = 2

        camera = RealSenseCamera(
            depth_resolution=RealSenseCamera.Resolution.RES_640x360,
            color_resolution=RealSenseCamera.Resolution.RES_640x360,
            frame_rate=RealSenseCamera.FrameRate.FPS_30,
            enable_decimation=True,
            decimation_magnitude=decimation,
        )

        with camera:
            frames = camera.get_frames()
            assert frames is not None
            assert len(frames) == 2
            (color_image, depth_image) = frames
            assert color_image.shape == (360, 640, 3)
            assert depth_image.shape == (360 // decimation, 640 // decimation)

    @realsense
    def test_temporal_filter(self):
        """Test temporal filter."""
        camera = RealSenseCamera(
            enable_temporal_filter=True,
        )

        with camera:
            pcd = camera.get_pointcloud()
            assert pcd is not None
            assert len(pcd.points) > 0


@pytest.mark.hardware
@realsense
class TestBoxTopDetectorWithRealSense:
    """Test BoxTopDetector."""

    def test_box_top_detector(self):
        """Test BoxTopDetector with real RealSense camera."""
        camera = RealSenseCamera()
        visualizer = NoOpVisualizer()

        detector = BoxTopDetector(camera, visualizer=visualizer)

        # Try to get a single detection
        box_top = detector.detect_single()

        # May be None if no box is visible, but should not crash
        assert box_top is None or hasattr(box_top, "center")

    def test_box_top_detector_streaming(self):
        """Test BoxTopDetector streaming."""
        camera = RealSenseCamera()
        visualizer = NoOpVisualizer()

        detector = BoxTopDetector(camera, visualizer=visualizer)

        # Test streaming for a short time
        count = 0
        for box_top in detector.detect_box_tops():
            count += 1
            if count >= 5:  # Just test a few frames
                break

        assert count > 0  # Should have processed some frames


@pytest.mark.hardware
@realsense
class TestVisualizationWithHardware:
    """Test visualization (slow tests)."""

    def test_open3d_visualizer_with_real_data(self):
        """Test Open3D visualizer with real camera data."""
        from box_top.visualizer import Open3DVisualizer

        camera = RealSenseCamera()

        with camera:
            pcd = camera.get_pointcloud()
            if pcd is not None and len(pcd.points) > 0:
                # Test that visualizer can handle real data
                visualizer = Open3DVisualizer()

                # Don't actually show the window in tests
                with patch.object(visualizer, "_visualizer") as mock_vis:
                    mock_vis.create_window.return_value = True
                    mock_vis.register_key_callback.return_value = True
                    mock_vis.get_render_option.return_value = type("MockRenderOption", (), {})()

                    with visualizer:
                        # Test update with real point cloud data
                        import open3d as o3d

                        obb = o3d.geometry.OrientedBoundingBox()
                        outlier_cloud = o3d.geometry.PointCloud()

                        result = visualizer.update(pcd, obb, outlier_cloud)
                        # Should work with real data
                        assert result
