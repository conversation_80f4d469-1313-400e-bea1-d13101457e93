"""Unit tests for points_to_transform module."""

import pytest
import numpy as np
from numpy.testing import assert_allclose

from calibration.points_to_transform import get_transform


class TestGetTransform:
    """Test cases for get_transform function."""

    def test_simple_identity_case(self):
        """Test get_transform with simple orthogonal unit vectors."""
        origin = np.array([0.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])

        transform = get_transform(origin, y_direction, x_direction)

        # Check that it's a 4x4 matrix
        assert transform.shape == (4, 4)

        # Check the rotation part (first 3x3)
        expected_rotation = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]])
        assert_allclose(transform[:3, :3], expected_rotation, atol=1e-10)

        # Check the translation part
        assert_allclose(transform[:3, 3], origin, atol=1e-10)

        # Check the bottom row
        assert_allclose(transform[3, :], [0.0, 0.0, 0.0, 1.0], atol=1e-10)

    def test_non_zero_origin(self):
        """Test get_transform with non-zero origin."""
        origin = np.array([1.0, 2.0, 3.0])
        y_direction = np.array([0.0, 1.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])

        transform = get_transform(origin, y_direction, x_direction)

        # Check the translation part
        assert_allclose(transform[:3, 3], origin, atol=1e-10)

    def test_orthogonalization(self):
        """Test that x_direction is properly orthogonalized to y_direction."""
        origin = np.array([0.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])
        # x_direction has a y component that should be removed
        x_direction = np.array([1.0, 0.5, 0.0])

        transform = get_transform(origin, y_direction, x_direction)

        # The resulting x and y vectors should be orthogonal
        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]

        # Check orthogonality (dot product should be zero)
        assert_allclose(np.dot(x_vec, y_vec), 0.0, atol=1e-10)

        # Check that both are unit vectors
        assert_allclose(np.linalg.norm(x_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(y_vec), 1.0, atol=1e-10)

    def test_normalization(self):
        """Test that direction vectors are properly normalized."""
        origin = np.array([0.0, 0.0, 0.0])
        y_direction = np.array([0.0, 2.0, 0.0])  # Not unit length
        x_direction = np.array([3.0, 0.0, 0.0])  # Not unit length

        transform = get_transform(origin, y_direction, x_direction)

        # All direction vectors should be unit length
        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]
        z_vec = transform[:3, 2]

        assert_allclose(np.linalg.norm(x_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(y_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(z_vec), 1.0, atol=1e-10)

    def test_right_handed_coordinate_system(self):
        """Test that the resulting coordinate system is right-handed."""
        origin = np.array([0.0, 0.0, 0.0])
        y_direction = np.array([0.0, 1.0, 0.0])
        x_direction = np.array([1.0, 0.0, 0.0])

        transform = get_transform(origin, y_direction, x_direction)

        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]
        z_vec = transform[:3, 2]

        # z should be x cross y for right-handed system
        expected_z = np.cross(x_vec, y_vec)
        assert_allclose(z_vec, expected_z, atol=1e-10)

    def test_arbitrary_directions(self):
        """Test with arbitrary direction vectors."""
        origin = np.array([1.0, -2.0, 3.0])
        y_direction = np.array([1.0, 1.0, 1.0])
        x_direction = np.array([1.0, -1.0, 0.0])

        transform = get_transform(origin, y_direction, x_direction)

        # Basic checks
        assert transform.shape == (4, 4)
        assert_allclose(transform[:3, 3], origin, atol=1e-10)
        assert_allclose(transform[3, :], [0.0, 0.0, 0.0, 1.0], atol=1e-10)

        # Check orthogonality and normalization
        x_vec = transform[:3, 0]
        y_vec = transform[:3, 1]
        z_vec = transform[:3, 2]

        assert_allclose(np.dot(x_vec, y_vec), 0.0, atol=1e-10)
        assert_allclose(np.dot(x_vec, z_vec), 0.0, atol=1e-10)
        assert_allclose(np.dot(y_vec, z_vec), 0.0, atol=1e-10)

        assert_allclose(np.linalg.norm(x_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(y_vec), 1.0, atol=1e-10)
        assert_allclose(np.linalg.norm(z_vec), 1.0, atol=1e-10)

    def test_no_scaling_in_transform(self):
        """Test that the transformation matrix contains no scaling."""
        origin = np.array([1.0, 2.0, 3.0])
        y_direction = np.array([0.0, 5.0, 0.0])  # Large magnitude
        x_direction = np.array([10.0, 0.0, 0.0])  # Large magnitude

        transform = get_transform(origin, y_direction, x_direction)

        # Extract the rotation part (3x3 upper-left)
        rotation_matrix = transform[:3, :3]

        # For a pure rotation matrix, the determinant should be 1
        det = np.linalg.det(rotation_matrix)
        assert_allclose(det, 1.0, atol=1e-10)

        # All columns should be unit vectors (no scaling)
        for i in range(3):
            column_norm = np.linalg.norm(rotation_matrix[:, i])
            assert_allclose(column_norm, 1.0, atol=1e-10)

    def test_parallel_input_vectors_error(self):
        """Test behavior when input vectors are parallel."""
        origin = np.array([0.0, 0.0, 0.0])
        y_direction = np.array([1.0, 0.0, 0.0])
        x_direction = np.array([2.0, 0.0, 0.0])  # Parallel to y_direction

        # Should raise ValueError for parallel vectors
        with pytest.raises(ValueError, match="cannot be parallel"):
            get_transform(origin, y_direction, x_direction)

    def test_zero_vector_input_error(self):
        """Test behavior with zero vector inputs."""
        origin = np.array([0.0, 0.0, 0.0])

        # Test with zero y_direction
        with pytest.raises(ValueError, match="y_direction cannot be a zero vector"):
            get_transform(origin, np.array([0.0, 0.0, 0.0]), np.array([1.0, 0.0, 0.0]))

        # Test with zero x_direction
        with pytest.raises(ValueError, match="x_direction cannot be a zero vector"):
            get_transform(origin, np.array([0.0, 1.0, 0.0]), np.array([0.0, 0.0, 0.0]))

    def test_transform_preserves_rotation_only(self):
        """Test that transform matrix represents only rotation and translation, no scaling."""
        origin = np.array([5.0, -3.0, 2.0])
        y_direction = np.array([1.0, 2.0, 3.0])
        x_direction = np.array([3.0, -1.0, 1.0])

        transform = get_transform(origin, y_direction, x_direction)

        # The 3x3 rotation part should be orthogonal
        rotation = transform[:3, :3]

        # R * R^T should equal identity for orthogonal matrices
        should_be_identity = rotation @ rotation.T
        assert_allclose(should_be_identity, np.eye(3), atol=1e-10)

        # Determinant should be 1 (not -1, which would be reflection)
        assert_allclose(np.linalg.det(rotation), 1.0, atol=1e-10)

    def test_specific_no_scaling_assertion(self):
        """Specific test to ensure get_transform never returns scaling transformations."""
        # Test multiple random cases to ensure no scaling ever occurs
        np.random.seed(42)  # For reproducible tests

        for _ in range(10):
            # Generate random vectors with various magnitudes
            origin = np.random.uniform(-10, 10, 3)
            y_direction = np.random.uniform(-100, 100, 3)
            x_direction = np.random.uniform(-100, 100, 3)

            # Ensure vectors are not zero or parallel
            while np.allclose(y_direction, 0) or np.allclose(x_direction, 0):
                y_direction = np.random.uniform(-100, 100, 3)
                x_direction = np.random.uniform(-100, 100, 3)

            # Ensure not parallel by checking if cross product is non-zero
            if np.allclose(np.cross(y_direction, x_direction), 0):
                x_direction[0] += 1.0  # Make them non-parallel

            transform = get_transform(origin, y_direction, x_direction)
            rotation = transform[:3, :3]

            # Check that all columns are unit vectors (no scaling)
            for i in range(3):
                column_norm = np.linalg.norm(rotation[:, i])
                assert_allclose(
                    column_norm,
                    1.0,
                    atol=1e-10,
                    err_msg=f"Column {i} is not unit length: {column_norm}",
                )

            # Check that determinant is 1 (no scaling, no reflection)
            det = np.linalg.det(rotation)
            assert_allclose(det, 1.0, atol=1e-10, err_msg=f"Determinant is not 1: {det}")

            # Check orthogonality (R * R^T = I)
            should_be_identity = rotation @ rotation.T
            assert_allclose(
                should_be_identity,
                np.eye(3),
                atol=1e-10,
                err_msg="Rotation matrix is not orthogonal",
            )

    def test_never_returns_scaling_transform(self):
        """
        Specific test to ensure get_transform NEVER returns a transform that indicates scaling.
        Only rotation and translation are expected.
        """
        # Test with various input magnitudes to ensure scaling is never introduced
        test_cases = [
            # Case 1: Very small vectors
            {
                "origin": np.array([0.0, 0.0, 0.0]),
                "y_direction": np.array([0.001, 0.0, 0.0]),
                "x_direction": np.array([0.0, 0.001, 0.0]),
            },
            # Case 2: Very large vectors
            {
                "origin": np.array([100.0, -200.0, 300.0]),
                "y_direction": np.array([1000.0, 0.0, 0.0]),
                "x_direction": np.array([0.0, 2000.0, 0.0]),
            },
            # Case 3: Mixed magnitude vectors
            {
                "origin": np.array([1.0, 2.0, 3.0]),
                "y_direction": np.array([0.1, 50.0, 0.0]),
                "x_direction": np.array([100.0, 0.01, 0.0]),
            },
            # Case 4: Non-orthogonal input that needs orthogonalization
            {
                "origin": np.array([0.0, 0.0, 0.0]),
                "y_direction": np.array([10.0, 20.0, 30.0]),
                "x_direction": np.array([5.0, 15.0, 25.0]),
            },
        ]

        for i, case in enumerate(test_cases):
            transform = get_transform(case["origin"], case["y_direction"], case["x_direction"])

            # Extract the 3x3 rotation part
            rotation = transform[:3, :3]

            # CRITICAL: Check that no scaling is present
            # 1. All columns must be unit vectors (length = 1)
            for col_idx in range(3):
                column = rotation[:, col_idx]
                column_length = np.linalg.norm(column)
                assert_allclose(
                    column_length,
                    1.0,
                    atol=1e-10,
                    err_msg=f"Case {i + 1}: Column {col_idx} has length {column_length}, expected 1.0 (no scaling allowed)",
                )

            # 2. Determinant must be exactly 1 (no scaling, no reflection)
            det = np.linalg.det(rotation)
            assert_allclose(
                det,
                1.0,
                atol=1e-10,
                err_msg=f"Case {i + 1}: Determinant is {det}, expected 1.0 (no scaling allowed)",
            )

            # 3. Matrix must be orthogonal (rotation only)
            should_be_identity = rotation @ rotation.T
            assert_allclose(
                should_be_identity,
                np.eye(3),
                atol=1e-10,
                err_msg=f"Case {i + 1}: Matrix is not orthogonal (scaling detected)",
            )

            # 4. Translation part should match input origin exactly
            translation = transform[:3, 3]
            assert_allclose(
                translation,
                case["origin"],
                atol=1e-10,
                err_msg=f"Case {i + 1}: Translation part modified unexpectedly",
            )

            # 5. Bottom row should be [0, 0, 0, 1] (homogeneous coordinates)
            bottom_row = transform[3, :]
            expected_bottom = np.array([0.0, 0.0, 0.0, 1.0])
            assert_allclose(
                bottom_row,
                expected_bottom,
                atol=1e-10,
                err_msg=f"Case {i + 1}: Bottom row is not [0, 0, 0, 1]",
            )
