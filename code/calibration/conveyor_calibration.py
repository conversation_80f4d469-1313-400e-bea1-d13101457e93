from sys import stdin
from loguru import logger
import cv2 as cv
import numpy as np
import tomlkit

from calibration.aruco_detection import detect_aruco
from calibration.points_to_transform import get_transform
from camera.camera_realsense import RealSenseCamera


class ConveyorCalibration:
    def __init__(self, camera):
        # read conveyor.toml
        self.config = tomlkit.load(open("config/conveyor.toml", "r+"))
        self.marker_ids: dict[str, int] = self.config.item("markers").unwrap()
        self.camera = camera

    def run(self):
        with self.camera:
            for _ in range(20):
                frames = self.camera.get_frames()  # Warm-up (temporal filter, white balance, ...)

            recording = False
            frames_to_record = 100
            recorded_coordinates = {id: [] for id in self.marker_ids.values()}

            while True:
                frames = self.camera.get_frames()
                if frames is None:
                    logger.warning("Failed to get frames")
                    continue
                color_image, depth_image, vertices = frames
                print(f"vertices range: {np.min(vertices[:, :, 2])}, {np.max(vertices[:, :, 2])}")
                print(f"depth range: {np.min(depth_image)}, {np.max(depth_image)}")
                if color_image.shape[:2] != depth_image.shape[:2]:
                    logger.error(
                        f"Color and depth image shapes do not match: {color_image.shape} vs {depth_image.shape}"
                        "This is probably because the decimation filter is enabled. Disable decimation."
                    )
                    raise RuntimeError(
                        "Color and depth image shapes do not match. Disable decimation."
                    )

                corners, ids = detect_aruco(color_image)
                if ids is not None:
                    for i, corner in enumerate(corners):
                        id = ids[i][0]
                        corner = corner[0]
                        if id not in self.marker_ids.values():
                            continue
                        cv.polylines(color_image, [corner.astype(int)], True, (0, 255, 0), 4)
                        cv.putText(
                            color_image,
                            f"{id}",
                            tuple(corner[0, :].astype(int)),
                            cv.FONT_HERSHEY_SIMPLEX,
                            1.0,
                            (0, 255, 0),
                            2,
                        )
                        # get average depth over area inside corners
                        mask = np.zeros(depth_image.shape, dtype=np.uint8)
                        cv.fillPoly(mask, [corner.astype(int)], 255)
                        depth_values = depth_image[mask == 255]
                        average_depth = np.mean(depth_values[depth_values > 0])
                        cv.putText(
                            color_image,
                            f"{average_depth:.2f}",
                            tuple(corner[1, :].astype(int)),
                            cv.FONT_HERSHEY_SIMPLEX,
                            1.0,
                            (0, 255, 0),
                            2,
                        )
                        cv.putText(
                            color_image,
                            f"{vertices[tuple(corner[0, ::-1].astype(int))]}",
                            tuple(corner[2, :].astype(int)),
                            cv.FONT_HERSHEY_SIMPLEX,
                            1.0,
                            (0, 255, 0),
                            2,
                        )

                        if recording:
                            # Save corner 0
                            coordinate = vertices[tuple(corner[0, ::-1].astype(int))]
                            coordinate[2] = (
                                average_depth / 1000  # More accurate as it's over the whole area
                            )
                            recorded_coordinates[id].append(coordinate)

                if not recording:
                    cv.putText(
                        color_image,
                        "Press space to start recording",
                        (10, 30),
                        cv.FONT_HERSHEY_SIMPLEX,
                        1.0,
                        (0, 255, 0),
                        2,
                    )
                else:
                    cv.putText(
                        color_image,
                        "Recording...",
                        (10, 30),
                        cv.FONT_HERSHEY_SIMPLEX,
                        1.0,
                        (0, 0, 255),
                        2,
                    )
                    frames_to_record -= 1
                    if frames_to_record == 0:
                        break

                cv.imshow("Color", color_image)
                cv.imshow("Depth", vertices[:, :, 2])
                # cv.imshow("Depth", depth_image)

                key = cv.waitKey(1)
                if key == 27:  # ESC
                    break
                if key == 32:  # SPACE
                    recording = not recording
                    if recording:
                        frames_to_record = 100
                        recorded_coordinates = {id: [] for id in self.marker_ids.values()}

            # resulting coordinates
            resulting_coordinates = {
                "upstream": None,
                "width": None,
                "downstream": None,
            }

            for id, data in recorded_coordinates.items():
                mean = np.mean(data, axis=0)
                std = np.std(data, axis=0)
                print(f"Marker {id}: {len(data)} frames recorded")
                print(f"  Average position: {mean}")
                print(f"  Standard deviation: {std}")
                if id == self.marker_ids["upstream_marker"]:
                    resulting_coordinates["upstream"] = mean
                elif id == self.marker_ids["width_marker"]:
                    resulting_coordinates["width"] = mean
                elif id == self.marker_ids["downstream_marker"]:
                    resulting_coordinates["downstream"] = mean

            assert resulting_coordinates["upstream"] is not None
            assert resulting_coordinates["width"] is not None
            assert resulting_coordinates["downstream"] is not None

            transform = get_transform(
                resulting_coordinates["upstream"],
                resulting_coordinates["width"] - resulting_coordinates["upstream"],
                resulting_coordinates["downstream"] - resulting_coordinates["upstream"],
            )

            answer = input("Save to conveyor.toml? (y/n)")
            if answer == "y":
                self.config["calibration"]["transform"] = transform.tolist()  # FIXME
                with open("config/conveyor.toml", "w") as f:
                    f.write(self.config.as_string())


if __name__ == "__main__":
    camera = RealSenseCamera(
        enable_decimation=False,
        enable_spatial_filter=False,
        enable_temporal_filter=False,
        enable_hole_filling=False,
    )
    conveyor_calibration = ConveyorCalibration(camera)

    conveyor_calibration.run()
