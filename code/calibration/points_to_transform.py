import numpy as np


def get_transform(origin, y_direction, x_direction):
    """
    Gets the transformation matrix from the origin and the two directions.

    Args:
        origin: The origin of the coordinate system.
        y_direction: The y direction of the coordinate system.
        x_direction: The x direction of the coordinate system. Note that this will be orthogonalized to the y direction.

    Returns:
        The transformation matrix.

    Raises:
        ValueError: If input vectors are zero, parallel, or would result in invalid transformation.
    """
    # Convert to numpy arrays and ensure they are float64
    origin = np.asarray(origin, dtype=np.float64)
    y_direction = np.asarray(y_direction, dtype=np.float64)
    x_direction = np.asarray(x_direction, dtype=np.float64)

    # Check for zero vectors
    y_norm = np.linalg.norm(y_direction)
    x_norm = np.linalg.norm(x_direction)

    if y_norm == 0.0:
        raise ValueError("y_direction cannot be a zero vector")
    if x_norm == 0.0:
        raise ValueError("x_direction cannot be a zero vector")

    # Normalize y_direction first
    y_direction = y_direction / y_norm

    # Orthogonalize x_direction to y_direction using Gram-Schmidt
    x_direction = x_direction - np.dot(x_direction, y_direction) * y_direction

    # Check if x_direction became zero after orthogonalization (parallel vectors)
    x_norm_after_ortho = np.linalg.norm(x_direction)
    if x_norm_after_ortho == 0.0:
        raise ValueError("x_direction and y_direction cannot be parallel")

    # Normalize the orthogonalized x_direction
    x_direction = x_direction / x_norm_after_ortho

    # Compute z_direction as cross product
    z_direction = np.cross(x_direction, y_direction)

    # Build the transformation matrix
    transform = np.eye(4)
    transform[:3, 0] = x_direction
    transform[:3, 1] = y_direction
    transform[:3, 2] = z_direction
    transform[:3, 3] = origin
    return transform
