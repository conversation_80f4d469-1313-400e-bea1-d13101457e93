import numpy as np


def get_transform(origin, y_direction, x_direction):
    """
    Gets the transformation matrix from the origin and the two directions.

    Args:
        origin: The origin of the coordinate system.
        y_direction: The y direction of the coordinate system.
        x_direction: The x direction of the coordinate system. Note that this will be orthogonalized to the y direction.

    Returns:
        The transformation matrix.
    """
    x_direction = (
        x_direction
        - np.dot(x_direction, y_direction) / np.dot(y_direction, y_direction) * y_direction
    )
    x_direction = x_direction / np.linalg.norm(x_direction)
    y_direction = y_direction / np.linalg.norm(y_direction)
    z_direction = np.cross(x_direction, y_direction)
    transform = np.eye(4)
    transform[:3, 0] = x_direction
    transform[:3, 1] = y_direction
    transform[:3, 2] = z_direction
    transform[:3, 3] = origin
    return transform
